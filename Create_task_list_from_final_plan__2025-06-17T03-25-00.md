[/] NAME:YogaBot Live SaaS Platform Development DESCRIPTION:Complete development of the YogaBot Live multi-tenant SaaS platform following the 6-phase development plan
-[/] NAME:Phase 1: Core SaaS Foundation DESCRIPTION:Build the application's core data models and business logic for a plan-based SaaS. No chat functionality will be built in this phase.
--[x] NAME:Install Core Dependencies DESCRIPTION:Install next-auth, @next-auth/prisma-adapter, bcryptjs, zod and other required dependencies for Phase 1
--[x] NAME:Implement Complete Database Schema DESCRIPTION:Implement the complete new schema in /prisma/schema.prisma with all models (User, Plan, Subscription, Chatbot, etc.) and run migration
--[x] NAME:Create Authentication System DESCRIPTION:Set up NextAuth.js with proper configuration for user authentication and session management
--[x] NAME:Build Admin Plan Management DESCRIPTION:Create /admin/plans page with full CRUD UI for Plan management and corresponding API routes
--[x] NAME:Build Admin User & Subscription Management DESCRIPTION:Create /admin/users page for creating Users with Subscriptions and initial Chatbot records, plus API routes
--[/] NAME:Implement Advanced Middleware DESCRIPTION:Create middleware.ts with authentication logic and usage limit checking for resource consumption
-[ ] NAME:Phase 2: Dual Knowledge Base System DESCRIPTION:Build the user-facing UIs and backend pipelines for both Knowledge Base types (Simple Text and Structured Form)
--[ ] NAME:Build User Dashboard UI DESCRIPTION:Create main dashboard page at /dashboard/kb that conditionally renders KB components based on user's plan
--[ ] NAME:Implement Simple Text KB DESCRIPTION:Create UI component with textarea and API route for saving simple text KB with background job for chunking and embeddings
--[ ] NAME:Implement Structured Form KB DESCRIPTION:Create complex multi-tabbed form UI mirroring form.md structure and API route for saving structured data with background processing
-[ ] NAME:Phase 3: Chat Engine & Usage Tracking DESCRIPTION:Build the end-to-end chat functionality, connecting the visitor widget to the intelligent backend
--[ ] NAME:Build Embeddable Chat Widget DESCRIPTION:Create /app/widget/[botId]/page.tsx with complete chat UI and server-side domain locking
--[ ] NAME:Implement Core Chat API DESCRIPTION:Build intelligent /api/chatbot/sendMessage route with hierarchical prompt engine, semantic search, and LLM integration
--[ ] NAME:Implement Usage Tracking DESCRIPTION:Add token counting and atomic increment of usage metrics on Subscription model
-[ ] NAME:Phase 4: Real-Time Layer & Live Chat Takeover DESCRIPTION:Implement live chat monitoring and takeover feature using Ably
--[ ] NAME:Install Ably and Setup Token Authentication DESCRIPTION:Install Ably dependency and create secure token authentication API route
--[ ] NAME:Implement Client-Side Real-Time Connections DESCRIPTION:Connect widget and dashboard to Ably using token auth flow
--[ ] NAME:Build Live Chat Takeover Backend DESCRIPTION:Create API routes for takeover functionality and modify sendMessage to act as central router
--[ ] NAME:Integrate Presence Indicators DESCRIPTION:Use Ably's Presence feature to show online visitors in user dashboard
-[ ] NAME:Phase 5: Advanced Settings & Admin Overrides DESCRIPTION:Build user-facing settings and powerful Admin override controls
--[ ] NAME:Build User Settings Page DESCRIPTION:Create /dashboard/settings with profile, widget config, SMTP config, and conditional BYOK section
--[ ] NAME:Build Admin Chatbot Management UI DESCRIPTION:Create /admin/chatbots/[chatbotId] page for editing all Chatbot fields including system prompts and LLM settings
--[ ] NAME:Update LLM Adapter DESCRIPTION:Modify /lib/llm.ts to prioritize BYOK keys over platform master keys
-[ ] NAME:Phase 6: Billing Integration DESCRIPTION:Automate the subscription and payment process with Razorpay
--[ ] NAME:Build Public Pricing Page DESCRIPTION:Create page displaying Plan details to prospective customers
--[ ] NAME:Implement Razorpay Checkout DESCRIPTION:Integrate Razorpay client-side library for checkout flow
--[ ] NAME:Implement Backend Subscription Creation DESCRIPTION:Create API route for initiating Razorpay subscriptions and creating Subscription records
--[ ] NAME:Implement Razorpay Webhook Endpoint DESCRIPTION:Create critical /api/webhooks/razorpay endpoint for handling subscription events and updating database